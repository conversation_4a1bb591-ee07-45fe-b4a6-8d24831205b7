package com.student;

/**
 * فئة تقرير الدرجات
 * تحتوي على معلومات شاملة عن نتائج حساب الدرجات
 */
public class GradeReport {
    private final double average;
    private final int totalStudents;
    private final int excellentCount;
    private final int failureCount;
    private final String description;
    private final String letterGrade;
    
    public GradeReport(double average, int totalStudents, int excellentCount, 
                      int failureCount, String description, String letterGrade) {
        this.average = average;
        this.totalStudents = totalStudents;
        this.excellentCount = excellentCount;
        this.failureCount = failureCount;
        this.description = description;
        this.letterGrade = letterGrade;
    }
    
    // Getters
    public double getAverage() {
        return average;
    }
    
    public int getTotalStudents() {
        return totalStudents;
    }
    
    public int getExcellentCount() {
        return excellentCount;
    }
    
    public int getFailureCount() {
        return failureCount;
    }
    
    public String getDescription() {
        return description;
    }
    
    public String getLetterGrade() {
        return letterGrade;
    }
    
    @Override
    public String toString() {
        return String.format(
            "تقرير الدرجات:\n" +
            "المتوسط: %.2f\n" +
            "عدد الطلاب: %d\n" +
            "عدد الممتازين: %d\n" +
            "عدد الراسبين: %d\n" +
            "التقدير: %s (%s)",
            average, totalStudents, excellentCount, failureCount, letterGrade, description
        );
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        GradeReport that = (GradeReport) obj;
        return Double.compare(that.average, average) == 0 &&
               totalStudents == that.totalStudents &&
               excellentCount == that.excellentCount &&
               failureCount == that.failureCount &&
               description.equals(that.description) &&
               letterGrade.equals(that.letterGrade);
    }
    
    @Override
    public int hashCode() {
        int result = Double.hashCode(average);
        result = 31 * result + totalStudents;
        result = 31 * result + excellentCount;
        result = 31 * result + failureCount;
        result = 31 * result + description.hashCode();
        result = 31 * result + letterGrade.hashCode();
        return result;
    }
}
