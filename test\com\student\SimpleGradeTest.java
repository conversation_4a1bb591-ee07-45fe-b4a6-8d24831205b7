package com.student;

import java.util.Arrays;
import java.util.List;
import java.util.ArrayList;

/**
 * اختبارات شاملة لفئة GradeCalculator
 * تغطي جميع الفروع والحالات المختلفة
 */
public class SimpleGradeTest {
    
    private GradeCalculator calculator;
    
    public SimpleGradeTest() {
        calculator = new GradeCalculator();
    }
    
    // اختبار 1: قائمة درجات فارغة - الفرع الأول
    public void testEmptyGradesList() {
        List<Double> emptyGrades = new ArrayList<>();
        GradeReport result = calculator.calculateGradeReport(emptyGrades, 0, false);
        
        assert result.getAverage() == 0.0 : "المتوسط يجب أن يكون 0";
        assert result.getTotalStudents() == 0 : "عدد الطلاب يجب أن يكون 0";
        assert "F".equals(result.getLetterGrade()) : "التقدير يجب أن يكون F";
        assert "لا توجد درجات".equals(result.getDescription()) : "الوصف غير صحيح";
        
        System.out.println("✓ اختبار القائمة الفارغة نجح");
    }
    
    // اختبار 2: قائمة درجات null - الفرع الأول
    public void testNullGradesList() {
        GradeReport result = calculator.calculateGradeReport(null, 0, false);
        
        assert result.getAverage() == 0.0 : "المتوسط يجب أن يكون 0";
        assert result.getTotalStudents() == 0 : "عدد الطلاب يجب أن يكون 0";
        assert "F".equals(result.getLetterGrade()) : "التقدير يجب أن يكون F";
        
        System.out.println("✓ اختبار القائمة null نجح");
    }
    
    // اختبار 3: درجات غير صحيحة - الفرع الثاني والخامس
    public void testInvalidGrades() {
        List<Double> invalidGrades = Arrays.asList(-10.0, 110.0, null);
        GradeReport result = calculator.calculateGradeReport(invalidGrades, 0, false);
        
        assert result.getAverage() == 0.0 : "المتوسط يجب أن يكون 0";
        assert result.getTotalStudents() == 0 : "عدد الطلاب يجب أن يكون 0";
        assert "لا توجد درجات صحيحة".equals(result.getDescription()) : "الوصف غير صحيح";
        
        System.out.println("✓ اختبار الدرجات غير الصحيحة نجح");
    }
    
    // اختبار 4: درجات ممتازة - الفرع الثالث
    public void testExcellentGrades() {
        List<Double> excellentGrades = Arrays.asList(95.0, 92.0, 98.0, 90.0);
        GradeReport result = calculator.calculateGradeReport(excellentGrades, 0, false);
        
        assert Math.abs(result.getAverage() - 93.75) < 0.01 : "المتوسط غير صحيح";
        assert result.getTotalStudents() == 4 : "عدد الطلاب غير صحيح";
        assert result.getExcellentCount() == 4 : "عدد الممتازين غير صحيح";
        assert result.getFailureCount() == 0 : "عدد الراسبين غير صحيح";
        assert "A".equals(result.getLetterGrade()) : "التقدير غير صحيح";
        
        System.out.println("✓ اختبار الدرجات الممتازة نجح");
    }
    
    // اختبار 5: درجات راسبة - الفرع الرابع
    public void testFailingGrades() {
        List<Double> failingGrades = Arrays.asList(45.0, 55.0, 30.0, 50.0);
        GradeReport result = calculator.calculateGradeReport(failingGrades, 0, false);
        
        assert Math.abs(result.getAverage() - 45.0) < 0.01 : "المتوسط غير صحيح";
        assert result.getTotalStudents() == 4 : "عدد الطلاب غير صحيح";
        assert result.getExcellentCount() == 0 : "عدد الممتازين غير صحيح";
        assert result.getFailureCount() == 4 : "عدد الراسبين غير صحيح";
        assert "F".equals(result.getLetterGrade()) : "التقدير غير صحيح";
        
        System.out.println("✓ اختبار الدرجات الراسبة نجح");
    }
    
    // اختبار 6: الوضع الصارم - الفرع السادس
    public void testStrictMode() {
        List<Double> grades = Arrays.asList(85.0, 88.0, 92.0, 87.0);
        
        GradeReport normalResult = calculator.calculateGradeReport(grades, 0, false);
        GradeReport strictResult = calculator.calculateGradeReport(grades, 0, true);
        
        assert Math.abs(normalResult.getAverage() - 88.0) < 0.01 : "المتوسط العادي غير صحيح";
        assert Math.abs(strictResult.getAverage() - 88.0) < 0.01 : "المتوسط الصارم غير صحيح";
        
        assert "B".equals(normalResult.getLetterGrade()) : "التقدير العادي غير صحيح";
        assert "B+".equals(strictResult.getLetterGrade()) : "التقدير الصارم غير صحيح";
        
        System.out.println("✓ اختبار الوضع الصارم نجح");
    }
    
    // اختبار 7: نقاط إضافية مع حد أقصى
    public void testBonusPointsWithMaxLimit() {
        List<Double> grades = Arrays.asList(95.0, 98.0, 92.0);
        GradeReport result = calculator.calculateGradeReport(grades, 10.0, false);
        
        assert Math.abs(result.getAverage() - 100.0) < 0.01 : "الحد الأقصى غير مطبق";
        assert "A".equals(result.getLetterGrade()) : "التقدير غير صحيح";
        
        System.out.println("✓ اختبار النقاط الإضافية مع الحد الأقصى نجح");
    }
    
    // اختبار 8: درجات مختلطة صحيحة وغير صحيحة
    public void testMixedValidInvalidGrades() {
        List<Double> mixedGrades = Arrays.asList(85.0, null, 110.0, 75.0, -5.0, 90.0);
        GradeReport result = calculator.calculateGradeReport(mixedGrades, 0, false);
        
        assert Math.abs(result.getAverage() - 83.33) < 0.01 : "المتوسط غير صحيح";
        assert result.getTotalStudents() == 3 : "عدد الطلاب غير صحيح";
        assert result.getExcellentCount() == 1 : "عدد الممتازين غير صحيح";
        
        System.out.println("✓ اختبار الدرجات المختلطة نجح");
    }
    
    // اختبار دالة المتوسط البسيط
    public void testSimpleAverage() {
        List<Double> grades = Arrays.asList(80.0, 90.0, 70.0);
        double average = calculator.calculateSimpleAverage(grades);
        assert Math.abs(average - 80.0) < 0.01 : "المتوسط البسيط غير صحيح";
        
        List<Double> emptyGrades = new ArrayList<>();
        double emptyAverage = calculator.calculateSimpleAverage(emptyGrades);
        assert emptyAverage == 0.0 : "متوسط القائمة الفارغة غير صحيح";
        
        System.out.println("✓ اختبار المتوسط البسيط نجح");
    }
    
    // اختبار التحقق من صحة الدرجات
    public void testValidGrades() {
        assert calculator.isValidGrade(85.0) : "85.0 يجب أن تكون درجة صحيحة";
        assert calculator.isValidGrade(0.0) : "0.0 يجب أن تكون درجة صحيحة";
        assert calculator.isValidGrade(100.0) : "100.0 يجب أن تكون درجة صحيحة";
        assert !calculator.isValidGrade(-1.0) : "-1.0 يجب أن تكون درجة غير صحيحة";
        assert !calculator.isValidGrade(101.0) : "101.0 يجب أن تكون درجة غير صحيحة";
        assert !calculator.isValidGrade(null) : "null يجب أن تكون درجة غير صحيحة";
        
        System.out.println("✓ اختبار صحة الدرجات نجح");
    }
    
    // تشغيل جميع الاختبارات
    public void runAllTests() {
        System.out.println("=== بدء تشغيل الاختبارات ===\n");
        
        try {
            testEmptyGradesList();
            testNullGradesList();
            testInvalidGrades();
            testExcellentGrades();
            testFailingGrades();
            testStrictMode();
            testBonusPointsWithMaxLimit();
            testMixedValidInvalidGrades();
            testSimpleAverage();
            testValidGrades();
            
            System.out.println("\n✅ جميع الاختبارات نجحت!");
            System.out.println("تم تغطية جميع الفروع الستة والحلقتين");
            
        } catch (AssertionError e) {
            System.out.println("❌ فشل في الاختبار: " + e.getMessage());
        } catch (Exception e) {
            System.out.println("❌ خطأ في الاختبار: " + e.getMessage());
        }
        
        System.out.println("\n=== انتهاء الاختبارات ===");
    }
    
    public static void main(String[] args) {
        SimpleGradeTest test = new SimpleGradeTest();
        test.runAllTests();
    }
}
