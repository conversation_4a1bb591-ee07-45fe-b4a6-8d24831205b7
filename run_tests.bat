@echo off
chcp 65001 >nul
echo ===== تشغيل مشروع حساب الدرجات =====
echo.

echo تجميع المشروع...
javac -d build\classes -cp . src\com\student\*.java
if %errorlevel% neq 0 (
    echo خطأ في التجميع!
    pause
    exit /b 1
)

echo تجميع الاختبارات...
javac -d build\test\classes -cp build\classes test\com\student\*.java
if %errorlevel% neq 0 (
    echo خطأ في تجميع الاختبارات!
    pause
    exit /b 1
)

echo.
echo ===== تشغيل المشروع الرئيسي =====
java -cp build\classes com.student.Main

echo.
echo ===== تشغيل الاختبارات =====
java -cp "build\test\classes;build\classes" com.student.GradeCalculatorTest

echo.
echo ===== انتهى التشغيل =====
pause
