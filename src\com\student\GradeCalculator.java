package com.student;

import java.util.ArrayList;
import java.util.List;

/**
 * فئة حساب درجات الطلاب مع تصنيفات مختلفة
 * تحتوي على دالة معقدة بـ 6 فروع وحلقتين
 */
public class GradeCalculator {
    
    /**
     * دالة معقدة لحساب وتصنيف درجات الطلاب
     * تحتوي على 6 فروع وحلقتين كما هو مطلوب
     * 
     * @param grades قائمة درجات الطلاب
     * @param bonusPoints نقاط إضافية
     * @param strictMode وضع صارم للتقييم
     * @return تقرير شامل عن الدرجات
     */
    public GradeReport calculateGradeReport(List<Double> grades, double bonusPoints, boolean strictMode) {
        if (grades == null || grades.isEmpty()) {
            // الفرع الأول: قائمة فارغة أو null
            return new GradeReport(0, 0, 0, 0, "لا توجد درجات", "F");
        }
        
        double totalGrades = 0;
        int validGrades = 0;
        int excellentCount = 0;
        int failureCount = 0;
        
        // الحلقة الأولى: حساب المجموع والتحقق من صحة الدرجات
        for (Double grade : grades) {
            if (grade == null) {
                continue; // تجاهل القيم الفارغة
            }
            
            if (grade < 0 || grade > 100) {
                // الفرع الثاني: درجة غير صحيحة
                continue; // تجاهل الدرجات غير الصحيحة
            }
            
            totalGrades += grade;
            validGrades++;
            
            if (grade >= 90) {
                // الفرع الثالث: درجة ممتازة
                excellentCount++;
            } else if (grade < 60) {
                // الفرع الرابع: درجة راسبة
                failureCount++;
            }
        }
        
        if (validGrades == 0) {
            // الفرع الخامس: لا توجد درجات صحيحة
            return new GradeReport(0, 0, 0, 0, "لا توجد درجات صحيحة", "F");
        }
        
        double average = totalGrades / validGrades;
        
        // إضافة النقاط الإضافية
        average += bonusPoints;
        
        // تطبيق الحد الأقصى للدرجة
        if (average > 100) {
            average = 100;
        }
        
        String letterGrade;
        String description;
        
        // الحلقة الثانية: تحديد التقدير النهائي بناءً على الوضع الصارم
        if (strictMode) {
            // الفرع السادس: الوضع الصارم
            if (average >= 95) {
                letterGrade = "A+";
                description = "ممتاز جداً (وضع صارم)";
            } else if (average >= 90) {
                letterGrade = "A";
                description = "ممتاز (وضع صارم)";
            } else if (average >= 85) {
                letterGrade = "B+";
                description = "جيد جداً (وضع صارم)";
            } else if (average >= 80) {
                letterGrade = "B";
                description = "جيد (وضع صارم)";
            } else if (average >= 75) {
                letterGrade = "C+";
                description = "مقبول جداً (وضع صارم)";
            } else if (average >= 70) {
                letterGrade = "C";
                description = "مقبول (وضع صارم)";
            } else if (average >= 65) {
                letterGrade = "D";
                description = "ضعيف (وضع صارم)";
            } else {
                letterGrade = "F";
                description = "راسب (وضع صارم)";
            }
        } else {
            // الوضع العادي
            if (average >= 90) {
                letterGrade = "A";
                description = "ممتاز";
            } else if (average >= 80) {
                letterGrade = "B";
                description = "جيد جداً";
            } else if (average >= 70) {
                letterGrade = "C";
                description = "جيد";
            } else if (average >= 60) {
                letterGrade = "D";
                description = "مقبول";
            } else {
                letterGrade = "F";
                description = "راسب";
            }
        }
        
        return new GradeReport(average, validGrades, excellentCount, failureCount, description, letterGrade);
    }
    
    /**
     * دالة مساعدة لحساب المتوسط البسيط
     */
    public double calculateSimpleAverage(List<Double> grades) {
        if (grades == null || grades.isEmpty()) {
            return 0.0;
        }
        
        double sum = 0;
        for (Double grade : grades) {
            if (grade != null) {
                sum += grade;
            }
        }
        
        return sum / grades.size();
    }
    
    /**
     * دالة للتحقق من صحة الدرجة
     */
    public boolean isValidGrade(Double grade) {
        return grade != null && grade >= 0 && grade <= 100;
    }
}
