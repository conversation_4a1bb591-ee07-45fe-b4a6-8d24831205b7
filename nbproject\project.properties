annotation.processing.enabled=true
annotation.processing.enabled.in.editor=false
annotation.processing.processor.options=
annotation.processing.processors.list=
annotation.processing.run.all.processors=true
annotation.processing.source.output=${build.generated.sources.dir}/ap-source-output
build.classes.dir=${build.dir}/classes
build.classes.excludes=**/*.java,**/*.form
build.dir=build
build.generated.dir=${build.dir}/generated
build.generated.sources.dir=${build.dir}/generated-sources
build.test.classes.dir=${build.dir}/test/classes
build.test.results.dir=${build.dir}/test/results
compile.on.save=true
compile.on.save.unsupported.javac=true
debug.classpath=${run.classpath}
debug.modulepath=\
    ${run.modulepath}
debug.test.classpath=${run.test.classpath}
debug.test.modulepath=\
    ${run.test.modulepath}
dist.dir=dist
dist.jar=${dist.dir}/GradeCalculator.jar
dist.javadoc.dir=${dist.dir}/javadoc
excludes=
includes=**
jar.compress=false
javac.classpath=
javac.compilerargs=
javac.deprecation=false
javac.external.vm=true
javac.modulepath=
javac.processormodulepath=
javac.processorpath=${javac.classpath}
javac.source=11
javac.target=11
javac.test.classpath=\
    ${javac.classpath}:\
    ${build.classes.dir}:\
    ${libs.junit_5.classpath}
javac.test.modulepath=\
    ${javac.modulepath}
javac.test.processorpath=${javac.test.classpath}
javadoc.additionalparam=
javadoc.author=false
javadoc.encoding=${source.encoding}
javadoc.noindex=false
javadoc.nonavbar=false
javadoc.notree=false
javadoc.private=false
javadoc.splitindex=true
javadoc.use=true
javadoc.version=false
javadoc.windowtitle=
main.class=com.student.Main
manifest.file=manifest.mf
meta.inf.dir=${src.dir}/META-INF
mkdist.disabled=false
platform.active=default_platform
run.classpath=${javac.classpath}:${build.classes.dir}
run.modulepath=\
    ${javac.modulepath}
run.test.classpath=${javac.test.classpath}:${build.test.classes.dir}
run.test.modulepath=\
    ${javac.test.modulepath}
source.encoding=UTF-8
src.dir=src
test.src.dir=test
