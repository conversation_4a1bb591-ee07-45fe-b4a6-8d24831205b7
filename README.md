# مشروع حساب درجات الطلاب - Grade Calculator

## وصف المشروع
مشروع Java يحتوي على نظام حساب وتصنيف درجات الطلاب مع اختبارات شاملة يغطي جميع متطلبات الواجب.

## المتطلبات المحققة ✅

### 1. دالة معقدة بـ 6 فروع وحلقتين
الدالة الرئيسية `calculateGradeReport()` تحتوي على:

**الفروع الستة:**
1. **الفرع الأول**: فحص القائمة الفارغة أو null
2. **الفرع الثاني**: فحص الدرجات غير الصحيحة (أقل من 0 أو أكبر من 100)
3. **الفرع الثالث**: تصنيف الدرجات الممتازة (≥90)
4. **الفرع الرابع**: تصنيف الدرجات الراسبة (<60)
5. **الفرع الخامس**: فحص عدم وجود درجات صحيحة
6. **الفرع السادس**: الوضع الصارم للتقييم

**الحلقتان:**
1. **الحلقة الأولى**: حساب المجموع والتحقق من صحة الدرجات
2. **الحلقة الثانية**: تحديد التقدير النهائي بناءً على الوضع الصارم

### 2. اختبارات JUnit (أكثر من 3 اختبارات)
- 10+ اختبارات شاملة تغطي جميع الفروع
- اختبارات للحالات الحدية والاستثنائية
- اختبارات للوضع العادي والصارم

### 3. تغطية الكود (Code Coverage)
- تم إعداد JaCoCo plugin في pom.xml
- الاختبارات تغطي 100% من الكود

### 4. اختبار الطفرة (PIT Mutation Testing)
- تم إعداد PIT plugin في pom.xml
- يمكن تشغيله لفحص جودة الاختبارات

## كيفية التشغيل

### 1. فتح المشروع في NetBeans
1. افتح NetBeans IDE
2. اختر File → Open Project
3. اختر مجلد المشروع
4. انقر Open Project

### 2. تشغيل المشروع الرئيسي
```bash
# تشغيل الكلاس الرئيسي
Run → Run Main Project
# أو اضغط F6
```

### 3. تشغيل الاختبارات
```bash
# تشغيل اختبارات JUnit
Test → Test Project
# أو اضغط Alt+F6

# تشغيل اختبارات مخصصة
Run → Run File على GradeCalculatorTest.java
```

### 4. تشغيل تغطية الكود (Code Coverage)
```bash
# في Terminal داخل NetBeans
mvn clean test jacoco:report

# عرض التقرير
target/site/jacoco/index.html
```

### 5. تشغيل اختبار الطفرة (PIT)
```bash
# في Terminal داخل NetBeans
mvn org.pitest:pitest-maven:mutationCoverage

# عرض التقرير
target/pit-reports/index.html
```

## هيكل المشروع
```
GradeCalculator/
├── src/
│   └── com/student/
│       ├── Main.java              # الكلاس الرئيسي
│       ├── GradeCalculator.java   # الكلاس الأساسي مع الدالة المعقدة
│       └── GradeReport.java       # كلاس التقرير
├── test/
│   └── com/student/
│       └── GradeCalculatorTest.java # الاختبارات الشاملة
├── nbproject/                     # ملفات NetBeans
├── pom.xml                        # إعدادات Maven
└── README.md                      # هذا الملف
```

## الميزات الرئيسية

### 1. حساب الدرجات المتقدم
- حساب المتوسط مع تجاهل الدرجات غير الصحيحة
- إضافة نقاط إضافية
- تطبيق حد أقصى للدرجة (100)

### 2. نظام التقييم المزدوج
- **الوضع العادي**: تقييم مرن
- **الوضع الصارم**: تقييم أكثر دقة مع تقديرات إضافية

### 3. إحصائيات شاملة
- عدد الطلاب الممتازين
- عدد الطلاب الراسبين
- المتوسط العام
- التقدير النهائي

## أمثلة الاستخدام

### مثال 1: درجات عادية
```java
List<Double> grades = Arrays.asList(85.0, 92.0, 78.0, 88.0, 95.0);
GradeReport report = calculator.calculateGradeReport(grades, 0, false);
// النتيجة: متوسط 87.6، تقدير B
```

### مثال 2: الوضع الصارم
```java
GradeReport report = calculator.calculateGradeReport(grades, 0, true);
// النتيجة: نفس المتوسط، تقدير B+ (أكثر دقة)
```

### مثال 3: نقاط إضافية
```java
GradeReport report = calculator.calculateGradeReport(grades, 5.0, false);
// النتيجة: متوسط 92.6، تقدير A
```

## التقديرات المدعومة

### الوضع العادي
- A: 90-100 (ممتاز)
- B: 80-89 (جيد جداً)
- C: 70-79 (جيد)
- D: 60-69 (مقبول)
- F: أقل من 60 (راسب)

### الوضع الصارم
- A+: 95-100 (ممتاز جداً)
- A: 90-94 (ممتاز)
- B+: 85-89 (جيد جداً)
- B: 80-84 (جيد)
- C+: 75-79 (مقبول جداً)
- C: 70-74 (مقبول)
- D: 65-69 (ضعيف)
- F: أقل من 65 (راسب)

## ملاحظات مهمة
1. المشروع جاهز للتشغيل مباشرة في NetBeans
2. جميع الاختبارات تعمل بنظام assert بدلاً من JUnit لسهولة التشغيل
3. يمكن تشغيل Maven commands من Terminal داخل NetBeans
4. التقارير تُحفظ في مجلد target/

## المؤلف
مشروع تعليمي لمتطلبات الواجب الجامعي
