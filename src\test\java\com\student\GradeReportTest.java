package com.student;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;

import static org.junit.jupiter.api.Assertions.*;

/**
 * اختبارات فئة GradeReport
 */
@DisplayName("اختبارات تقرير الدرجات")
class GradeReportTest {
    
    @Test
    @DisplayName("اختبار إنشاء تقرير الدرجات")
    void testGradeReportCreation() {
        GradeReport report = new GradeReport(85.5, 10, 3, 1, "جيد جداً", "B");
        
        assertEquals(85.5, report.getAverage(), 0.01);
        assertEquals(10, report.getTotalStudents());
        assertEquals(3, report.getExcellentCount());
        assertEquals(1, report.getFailureCount());
        assertEquals("جيد جداً", report.getDescription());
        assertEquals("B", report.getLetterGrade());
    }
    
    @Test
    @DisplayName("اختبار toString")
    void testToString() {
        GradeReport report = new GradeReport(88.75, 4, 2, 0, "ممتاز", "A");
        String expected = "تقرير الدرجات:\n" +
                         "المتوسط: 88.75\n" +
                         "عدد الطلاب: 4\n" +
                         "عدد الممتازين: 2\n" +
                         "عدد الراسبين: 0\n" +
                         "التقدير: A (ممتاز)";
        
        assertEquals(expected, report.toString());
    }
    
    @Test
    @DisplayName("اختبار equals و hashCode")
    void testEqualsAndHashCode() {
        GradeReport report1 = new GradeReport(85.0, 5, 2, 1, "جيد", "B");
        GradeReport report2 = new GradeReport(85.0, 5, 2, 1, "جيد", "B");
        GradeReport report3 = new GradeReport(90.0, 5, 2, 1, "ممتاز", "A");
        
        // اختبار equals
        assertEquals(report1, report2);
        assertNotEquals(report1, report3);
        assertNotEquals(report1, null);
        assertNotEquals(report1, "string");
        
        // اختبار hashCode
        assertEquals(report1.hashCode(), report2.hashCode());
        assertNotEquals(report1.hashCode(), report3.hashCode());
        
        // اختبار equals مع نفس الكائن
        assertEquals(report1, report1);
    }
}
