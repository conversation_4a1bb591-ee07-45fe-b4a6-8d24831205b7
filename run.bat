@echo off
echo ===== Grade Calculator Project =====
echo.

echo Compiling main project...
javac -d build\classes src\com\student\*.java
if %errorlevel% neq 0 (
    echo Compilation failed!
    pause
    exit /b 1
)

echo Compiling tests...
javac -d build\test\classes -cp build\classes test\com\student\*.java
if %errorlevel% neq 0 (
    echo Test compilation failed!
    pause
    exit /b 1
)

echo.
echo ===== Running Main Project =====
java -cp build\classes com.student.Main

echo.
echo ===== Running Tests =====
java -ea -cp "build\test\classes;build\classes" com.student.SimpleGradeTest

echo.
echo ===== Project completed successfully =====
pause
