package com.student;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;

import java.util.Arrays;
import java.util.List;
import java.util.ArrayList;

import static org.junit.jupiter.api.Assertions.*;

/**
 * اختبارات شاملة لفئة GradeCalculator
 * تغطي جميع الفروع والحالات المختلفة
 */
@DisplayName("اختبارات حساب الدرجات")
class GradeCalculatorTest {
    
    private GradeCalculator calculator;
    
    @BeforeEach
    void setUp() {
        calculator = new GradeCalculator();
    }
    
    @Test
    @DisplayName("اختبار 1: قائمة درجات فارغة - الفرع الأول")
    void testEmptyGradesList() {
        // ترتيب البيانات
        List<Double> emptyGrades = new ArrayList<>();
        
        // تنفيذ الاختبار
        GradeReport result = calculator.calculateGradeReport(emptyGrades, 0, false);
        
        // التحقق من النتائج
        assertEquals(0.0, result.getAverage(), 0.01);
        assertEquals(0, result.getTotalStudents());
        assertEquals("F", result.getLetterGrade());
        assertEquals("لا توجد درجات", result.getDescription());
    }
    
    @Test
    @DisplayName("اختبار 2: قائمة درجات null - الفرع الأول")
    void testNullGradesList() {
        // تنفيذ الاختبار
        GradeReport result = calculator.calculateGradeReport(null, 0, false);
        
        // التحقق من النتائج
        assertEquals(0.0, result.getAverage(), 0.01);
        assertEquals(0, result.getTotalStudents());
        assertEquals("F", result.getLetterGrade());
        assertEquals("لا توجد درجات", result.getDescription());
    }
    
    @Test
    @DisplayName("اختبار 3: درجات غير صحيحة - الفرع الثاني والخامس")
    void testInvalidGrades() {
        // ترتيب البيانات - درجات غير صحيحة فقط
        List<Double> invalidGrades = Arrays.asList(-10.0, 110.0, null);
        
        // تنفيذ الاختبار
        GradeReport result = calculator.calculateGradeReport(invalidGrades, 0, false);
        
        // التحقق من النتائج
        assertEquals(0.0, result.getAverage(), 0.01);
        assertEquals(0, result.getTotalStudents());
        assertEquals("F", result.getLetterGrade());
        assertEquals("لا توجد درجات صحيحة", result.getDescription());
    }
    
    @Test
    @DisplayName("اختبار 4: درجات ممتازة - الفرع الثالث")
    void testExcellentGrades() {
        // ترتيب البيانات - درجات ممتازة
        List<Double> excellentGrades = Arrays.asList(95.0, 92.0, 98.0, 90.0);
        
        // تنفيذ الاختبار
        GradeReport result = calculator.calculateGradeReport(excellentGrades, 0, false);
        
        // التحقق من النتائج
        assertEquals(93.75, result.getAverage(), 0.01);
        assertEquals(4, result.getTotalStudents());
        assertEquals(4, result.getExcellentCount());
        assertEquals(0, result.getFailureCount());
        assertEquals("A", result.getLetterGrade());
        assertEquals("ممتاز", result.getDescription());
    }
    
    @Test
    @DisplayName("اختبار 5: درجات راسبة - الفرع الرابع")
    void testFailingGrades() {
        // ترتيب البيانات - درجات راسبة
        List<Double> failingGrades = Arrays.asList(45.0, 55.0, 30.0, 50.0);
        
        // تنفيذ الاختبار
        GradeReport result = calculator.calculateGradeReport(failingGrades, 0, false);
        
        // التحقق من النتائج
        assertEquals(45.0, result.getAverage(), 0.01);
        assertEquals(4, result.getTotalStudents());
        assertEquals(0, result.getExcellentCount());
        assertEquals(4, result.getFailureCount());
        assertEquals("F", result.getLetterGrade());
        assertEquals("راسب", result.getDescription());
    }
    
    @Test
    @DisplayName("اختبار 6: الوضع الصارم - الفرع السادس")
    void testStrictMode() {
        // ترتيب البيانات
        List<Double> grades = Arrays.asList(85.0, 88.0, 92.0, 87.0);
        
        // تنفيذ الاختبار - الوضع العادي
        GradeReport normalResult = calculator.calculateGradeReport(grades, 0, false);
        
        // تنفيذ الاختبار - الوضع الصارم
        GradeReport strictResult = calculator.calculateGradeReport(grades, 0, true);
        
        // التحقق من النتائج
        assertEquals(88.0, normalResult.getAverage(), 0.01);
        assertEquals(88.0, strictResult.getAverage(), 0.01);
        
        // في الوضع العادي: 88 = B
        assertEquals("B", normalResult.getLetterGrade());
        assertEquals("جيد جداً", normalResult.getDescription());
        
        // في الوضع الصارم: 88 = B+
        assertEquals("B+", strictResult.getLetterGrade());
        assertEquals("جيد جداً (وضع صارم)", strictResult.getDescription());
    }
    
    @Test
    @DisplayName("اختبار 7: نقاط إضافية مع حد أقصى")
    void testBonusPointsWithMaxLimit() {
        // ترتيب البيانات
        List<Double> grades = Arrays.asList(95.0, 98.0, 92.0);
        
        // تنفيذ الاختبار مع نقاط إضافية كبيرة
        GradeReport result = calculator.calculateGradeReport(grades, 10.0, false);
        
        // التحقق من النتائج - يجب أن يكون الحد الأقصى 100
        assertEquals(100.0, result.getAverage(), 0.01);
        assertEquals("A", result.getLetterGrade());
    }
    
    @Test
    @DisplayName("اختبار 8: درجات مختلطة صحيحة وغير صحيحة")
    void testMixedValidInvalidGrades() {
        // ترتيب البيانات - خليط من الدرجات الصحيحة وغير الصحيحة
        List<Double> mixedGrades = Arrays.asList(85.0, null, 110.0, 75.0, -5.0, 90.0);
        
        // تنفيذ الاختبار
        GradeReport result = calculator.calculateGradeReport(mixedGrades, 0, false);
        
        // التحقق من النتائج - يجب أن يحسب فقط الدرجات الصحيحة: 85, 75, 90
        assertEquals(83.33, result.getAverage(), 0.01);
        assertEquals(3, result.getTotalStudents());
        assertEquals(1, result.getExcellentCount()); // 90 فقط
        assertEquals(0, result.getFailureCount());
    }
    
    @Test
    @DisplayName("اختبار 9: جميع تقديرات الوضع الصارم")
    void testAllStrictModeGrades() {
        // اختبار A+ في الوضع الصارم
        List<Double> gradeA_plus = Arrays.asList(96.0, 97.0, 98.0);
        GradeReport resultA_plus = calculator.calculateGradeReport(gradeA_plus, 0, true);
        assertEquals("A+", resultA_plus.getLetterGrade());
        assertEquals("ممتاز جداً (وضع صارم)", resultA_plus.getDescription());
        
        // اختبار D في الوضع الصارم
        List<Double> gradeD = Arrays.asList(65.0, 67.0, 68.0);
        GradeReport resultD = calculator.calculateGradeReport(gradeD, 0, true);
        assertEquals("D", resultD.getLetterGrade());
        assertEquals("ضعيف (وضع صارم)", resultD.getDescription());
    }
    
    @Test
    @DisplayName("اختبار 10: دالة المتوسط البسيط")
    void testSimpleAverage() {
        List<Double> grades = Arrays.asList(80.0, 90.0, 70.0);
        double average = calculator.calculateSimpleAverage(grades);
        assertEquals(80.0, average, 0.01);
        
        // اختبار قائمة فارغة
        List<Double> emptyGrades = new ArrayList<>();
        double emptyAverage = calculator.calculateSimpleAverage(emptyGrades);
        assertEquals(0.0, emptyAverage, 0.01);
    }
    
    @ParameterizedTest
    @ValueSource(doubles = {85.0, 0.0, 100.0, 50.5})
    @DisplayName("اختبار 11: التحقق من صحة الدرجات - درجات صحيحة")
    void testValidGrades(double grade) {
        assertTrue(calculator.isValidGrade(grade));
    }
    
    @ParameterizedTest
    @ValueSource(doubles = {-1.0, 101.0, -50.0, 150.0})
    @DisplayName("اختبار 12: التحقق من صحة الدرجات - درجات غير صحيحة")
    void testInvalidGradeValues(double grade) {
        assertFalse(calculator.isValidGrade(grade));
    }
    
    @Test
    @DisplayName("اختبار 13: التحقق من صحة الدرجة null")
    void testNullGradeValidation() {
        assertFalse(calculator.isValidGrade(null));
    }
}
