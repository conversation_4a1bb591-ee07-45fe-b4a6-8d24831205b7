package com.student;

import java.util.Arrays;
import java.util.List;

/**
 * الكلاس الرئيسي لتشغيل المشروع
 */
public class Main {
    public static void main(String[] args) {
        System.out.println("=== مشروع حساب درجات الطلاب ===\n");
        
        GradeCalculator calculator = new GradeCalculator();
        
        // مثال 1: درجات عادية
        List<Double> grades1 = Arrays.asList(85.0, 92.0, 78.0, 88.0, 95.0);
        GradeReport report1 = calculator.calculateGradeReport(grades1, 0, false);
        System.out.println("المثال الأول - درجات عادية:");
        System.out.println(report1);
        System.out.println();
        
        // مثال 2: نفس الدرجات مع الوضع الصارم
        GradeReport report2 = calculator.calculateGradeReport(grades1, 0, true);
        System.out.println("المثال الثاني - نفس الدرجات مع الوضع الصارم:");
        System.out.println(report2);
        System.out.println();
        
        // مثال 3: درجات مع نقاط إضافية
        List<Double> grades3 = Arrays.asList(75.0, 68.0, 82.0, 79.0);
        GradeReport report3 = calculator.calculateGradeReport(grades3, 5.0, false);
        System.out.println("المثال الثالث - درجات مع 5 نقاط إضافية:");
        System.out.println(report3);
        System.out.println();
        
        // مثال 4: درجات تحتوي على قيم غير صحيحة
        List<Double> grades4 = Arrays.asList(85.0, null, 110.0, -5.0, 92.0, 78.0);
        GradeReport report4 = calculator.calculateGradeReport(grades4, 0, false);
        System.out.println("المثال الرابع - درجات تحتوي على قيم غير صحيحة:");
        System.out.println(report4);
        System.out.println();
        
        // مثال 5: قائمة فارغة
        List<Double> grades5 = Arrays.asList();
        GradeReport report5 = calculator.calculateGradeReport(grades5, 0, false);
        System.out.println("المثال الخامس - قائمة فارغة:");
        System.out.println(report5);
        System.out.println();
        
        System.out.println("=== انتهى تشغيل المشروع ===");
    }
}
