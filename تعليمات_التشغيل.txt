🎯 مشروع حساب درجات الطلاب - تعليمات التشغيل السريع

📁 المشروع جاهز للتشغيل في NetBeans!

🚀 طرق التشغيل:

1️⃣ التشغيل في NetBeans:
   - افتح NetBeans IDE
   - File → Open Project
   - اختر مجلد المشروع
   - اضغط F6 لتشغيل المشروع الرئيسي
   - اضغط Alt+F6 لتشغيل الاختبارات

2️⃣ التشغيل السريع (Windows):
   - انقر مرتين على ملف run_tests.bat
   - سيشغل المشروع والاختبارات تلقائياً

3️⃣ التشغيل اليدوي:
   أ) تجميع المشروع:
      javac -d build\classes src\com\student\*.java
   
   ب) تشغيل المشروع:
      java -cp build\classes com.student.Main
   
   ج) تجميع الاختبارات:
      javac -d build\test\classes -cp build\classes test\com\student\*.java
   
   د) تشغيل الاختبارات:
      java -cp "build\test\classes;build\classes" com.student.GradeCalculatorTest

📊 المتطلبات المحققة:
✅ دالة معقدة بـ 6 فروع وحلقتين
✅ اختبارات شاملة (أكثر من 3 اختبارات)
✅ تغطية كاملة للكود
✅ إعداد PIT للاختبار الطفرة

📋 ملفات المشروع:
- src/com/student/GradeCalculator.java (الكلاس الرئيسي)
- src/com/student/GradeReport.java (كلاس التقرير)
- src/com/student/Main.java (نقطة البداية)
- test/com/student/GradeCalculatorTest.java (الاختبارات)
- pom.xml (إعدادات Maven للـ Code Coverage و PIT)
- README.md (التوثيق الكامل)

🎯 الفروع الستة في الكود:
1. فحص القائمة الفارغة/null
2. فحص الدرجات غير الصحيحة
3. تصنيف الدرجات الممتازة
4. تصنيف الدرجات الراسبة
5. فحص عدم وجود درجات صحيحة
6. الوضع الصارم للتقييم

🔄 الحلقتان:
1. حلقة حساب المجموع والتحقق
2. حلقة تحديد التقدير النهائي

📈 لتشغيل Code Coverage:
mvn clean test jacoco:report

🧬 لتشغيل PIT Mutation Testing:
mvn org.pitest:pitest-maven:mutationCoverage

✨ المشروع جاهز للتسليم!
